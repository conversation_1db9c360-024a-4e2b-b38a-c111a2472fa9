<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bace45f6-d73e-4cf0-a90c-b9c79db592f7" name="更改" comment="fix:修改登录框默认值">
      <change beforePath="$PROJECT_DIR$/hotely5d-web/src/api/member.js" beforeDir="false" afterPath="$PROJECT_DIR$/hotely5d-web/src/api/member.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hotely5d-web/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/hotely5d-web/src/router/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hotely5d-web/src/views/AdminLayout.vue" beforeDir="false" afterPath="$PROJECT_DIR$/hotely5d-web/src/views/AdminLayout.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hotely5d/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/hotely5d/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hotely5d/src/main/java/com/hotely5d/controller/MemberController.java" beforeDir="false" afterPath="$PROJECT_DIR$/hotely5d/src/main/java/com/hotely5d/controller/MemberController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hotely5d/src/main/java/com/hotely5d/controller/OrdersController.java" beforeDir="false" afterPath="$PROJECT_DIR$/hotely5d/src/main/java/com/hotely5d/controller/OrdersController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/org/mybatis/mybatis/3.4.6/mybatis-3.4.6.jar!/org/apache/ibatis/session/defaults/DefaultSqlSession.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$MAVEN_REPOSITORY$/org/springframework/spring-web/5.0.5.RELEASE/spring-web-5.0.5.RELEASE.jar!/org/springframework/web/bind/annotation/RequestBody.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\develop\maven\apache-maven-3.9.9" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\develop\maven\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2z5BltufzGEHca9daB1jTItxJ9s" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "找到数据库连接形参",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.Application.executor": "Run",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/project/hotel",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "preferences.fileTypes",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="hotely5d" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hotely5d.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="bace45f6-d73e-4cf0-a90c-b9c79db592f7" name="更改" comment="" />
      <created>1751011207349</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751011207349</updated>
      <workItem from="1751011209629" duration="4742000" />
      <workItem from="1751071666289" duration="10496000" />
      <workItem from="1751091662722" duration="1386000" />
      <workItem from="1751185691574" duration="17000" />
      <workItem from="1751327847210" duration="2399000" />
    </task>
    <task id="LOCAL-00001" summary="fix:修改文件存放路径">
      <option name="closed" value="true" />
      <created>1751011542429</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751011542429</updated>
    </task>
    <task id="LOCAL-00002" summary="fix:修改登录框默认值">
      <option name="closed" value="true" />
      <created>1751016804996</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751016804996</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix:修改文件存放路径" />
    <MESSAGE value="fix:修改登录框默认值" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:修改登录框默认值" />
  </component>
</project>