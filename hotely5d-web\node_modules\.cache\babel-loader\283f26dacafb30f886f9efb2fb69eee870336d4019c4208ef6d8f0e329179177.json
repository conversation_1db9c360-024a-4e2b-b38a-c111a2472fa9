{"ast": null, "code": "import { mapState, mapMutations } from 'vuex';\nimport { removeToken } from '@/utils/auth';\nexport default {\n  name: \"Home\",\n  computed: {\n    ...mapState(['user']) // 映射 Vuex 中的 user 状态到当前组件的计算属性\n  },\n\n  data() {\n    return {\n      isCollapse: false // 控制菜单栏是否折叠的状态\n    };\n  },\n\n  methods: {\n    ...mapMutations(['setUser', 'setToken']),\n    logout() {\n      this.$confirm('确认退出登录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeToken(); // 移除用户 token\n        this.setUser({}); // 清空用户信息\n        this.setToken(''); // 清空 token\n        this.$router.replace('/front'); // 跳转到前台页面\n      }).catch(() => {});\n    }\n  }\n};", "map": {"version": 3, "names": ["mapState", "mapMutations", "removeToken", "name", "computed", "data", "isCollapse", "methods", "logout", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "setUser", "setToken", "$router", "replace", "catch"], "sources": ["src/views/AdminLayout.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"{ home: true, openMenus: !isCollapse, closeMenus: isCollapse }\">\r\n    <div class=\"menus\">\r\n      <div class=\"text-center font-bold text-white pt-20 pb-10 flex items-center justify-center\">\r\n        <img src=\"@/assets/logo.jpg\" height=\"30px\"/>{{ $sysTitle }}后台\r\n      </div>\r\n      <el-menu :collapse=\"isCollapse\" default-active=\"home\" background-color=\"#304156\" text-color=\"#fff\"\r\n        active-text-color=\"#409eff\" router>\r\n        <el-menu-item index=\"/admin\">\r\n          <i class=\"el-icon-s-home\"></i>\r\n          <span slot=\"title\">首页</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/admin\">\r\n          <i class=\"el-icon-user\"></i>\r\n          <span slot=\"title\">管理员管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/member\">\r\n          <i class=\"el-icon-user-solid\"></i>\r\n          <span slot=\"title\">用户管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/category\">\r\n          <i class=\"el-icon-s-promotion\"></i>\r\n          <span slot=\"title\">房型管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/room\">\r\n          <i class=\"el-icon-s-cooperation\"></i>\r\n          <span slot=\"title\">房间管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/appointment\">\r\n          <i class=\"el-icon-s-flag\"></i>\r\n          <span slot=\"title\">预约管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/orders\">\r\n          <i class=\"el-icon-s-order\"></i>\r\n          <span slot=\"title\">入住管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/message\">\r\n          <i class=\"el-icon-warning\"></i>\r\n          <span slot=\"title\">留言管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/notice\">\r\n          <i class=\"el-icon-bell\"></i>\r\n          <span slot=\"title\">公告管理</span>\r\n        </el-menu-item>\r\n        <el-menu-item index=\"/admin/excel-demo\">\r\n          <i class=\"el-icon-download\"></i>\r\n          <span slot=\"title\">Excel导入导出</span>\r\n        </el-menu-item>\r\n\r\n        <!-- <el-submenu index>\r\n          <template slot=\"title\">\r\n            <i class=\"el-icon-location\"></i>\r\n            <span>用户管理</span>\r\n          </template>\r\n          <el-menu-item index=\"/account\">\r\n          <i class=\"el-icon-setting\"></i>\r\n          <span slot=\"title\">查询用户</span>\r\n        </el-menu-item>\r\n        </el-submenu> -->\r\n      </el-menu>\r\n    </div>\r\n    <div class=\"content\">\r\n      <div class=\"navbar\">\r\n        <div class=\"navbar-btn\" @click=\"isCollapse = !isCollapse\">\r\n          <i class=\"el-icon-s-fold\" v-if=\"!isCollapse\"></i>\r\n          <i class=\"el-icon-s-unfold\" v-else></i>\r\n        </div>\r\n        <div class=\"navbar-right\">\r\n          <el-dropdown trigger=\"click\">\r\n            <span class=\"el-dropdown-link\">\r\n              {{ user.name }}<i class=\"el-icon-caret-bottom el-icon--right\"></i>\r\n            </span>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item icon=\"el-icon-s-custom\" @click.native=\"$router.push('/admin/adminInfo')\">个人信息</el-dropdown-item>\r\n              <el-dropdown-item icon=\"el-icon-caret-left\" @click.native=\"logout\">退出登录</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </div>\r\n      </div>\r\n      <!-- 内容部分 -->\r\n      <div class=\"section\">\r\n        <router-view></router-view>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState,mapMutations } from 'vuex'\r\nimport { removeToken } from '@/utils/auth'\r\nexport default {\r\n  name: \"Home\",\r\n  computed: {\r\n    ...mapState(['user'])  // 映射 Vuex 中的 user 状态到当前组件的计算属性\r\n  },\r\n  data() {\r\n    return {\r\n      isCollapse: false,  // 控制菜单栏是否折叠的状态\r\n    };\r\n  },\r\n  methods: {\r\n    ...mapMutations(['setUser', 'setToken']),\r\n    logout() {\r\n      this.$confirm('确认退出登录吗?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        removeToken()  // 移除用户 token\r\n        this.setUser({})  // 清空用户信息\r\n        this.setToken('')  // 清空 token\r\n        this.$router.replace('/front')  // 跳转到前台页面\r\n      }).catch(() => { });\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.openMenus {\r\n  .menus {\r\n    height: 100vh;\r\n    width: 200px;\r\n    overflow-y: auto;\r\n    position: fixed;\r\n    left: 0;\r\n    top: 0;\r\n    background-color: #304156;\r\n    transition: all 0.5s;\r\n\r\n    .el-menu {\r\n      border-right: none;\r\n    }\r\n  }\r\n\r\n  .content {\r\n    // background: darkcyan;\r\n    margin-left: 200px;\r\n    width: calc(100% - 200px);\r\n    height: 100vh;\r\n    transition: all 0.5s;\r\n  }\r\n}\r\n\r\n.closeMenus {\r\n  .menus {\r\n    height: 100vh;\r\n    width: 64px;\r\n    overflow-y: auto;\r\n    position: fixed;\r\n    left: 0;\r\n    top: 0;\r\n    transition: all 0.5s;\r\n    background-color: #304156;\r\n\r\n    .el-menu {\r\n      border-right: none;\r\n    }\r\n  }\r\n\r\n  .content {\r\n    // background: darkcyan;\r\n    margin-left: 64px;\r\n    width: calc(100% - 64px);\r\n    height: 100vh;\r\n    transition: all 0.5s;\r\n  }\r\n}\r\n\r\n.home {\r\n  .navbar {\r\n    height: 60px;\r\n    // background-color: darkcyan;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 10px;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid #eee;\r\n\r\n    .navbar-btn {\r\n      // width: 60px;\r\n      height: 40px;\r\n      line-height: 40px;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      // background-color: darkblue;\r\n    }\r\n\r\n    .navbar-right {\r\n      min-width: 60px;\r\n      height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n      // background-color: darkmagenta;\r\n    }\r\n  }\r\n\r\n  .content {\r\n    .section {\r\n      padding: 16px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAwFA,SAAAA,QAAA,EAAAC,YAAA;AACA,SAAAC,WAAA;AACA;EACAC,IAAA;EACAC,QAAA;IACA,GAAAJ,QAAA;EACA;;EACAK,KAAA;IACA;MACAC,UAAA;IACA;EACA;;EACAC,OAAA;IACA,GAAAN,YAAA;IACAO,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACAX,WAAA;QACA,KAAAY,OAAA;QACA,KAAAC,QAAA;QACA,KAAAC,OAAA,CAAAC,OAAA;MACA,GAAAC,KAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}