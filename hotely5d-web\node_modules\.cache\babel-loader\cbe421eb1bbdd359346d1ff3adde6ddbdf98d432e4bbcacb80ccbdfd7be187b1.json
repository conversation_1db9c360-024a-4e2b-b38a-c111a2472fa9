{"ast": null, "code": "import request from '@/utils/request';\n\n//根据条件分页查询\nexport const findMemberPageAPI = (pageNum, pageSize, params) => {\n  return request({\n    url: `/member/search/${pageNum}/${pageSize}`,\n    method: 'get',\n    params\n  });\n};\n\n//根据条件查询\nexport const findMemberAPI = params => {\n  return request({\n    url: '/member/search',\n    method: 'get',\n    params\n  });\n};\n\n//查询全部\nexport const findAllMemberAPI = () => {\n  return request({\n    url: '/member/',\n    method: 'get'\n  });\n};\n\n//新增\nexport const addMemberAPI = data => {\n  return request({\n    url: '/member/',\n    method: 'post',\n    data\n  });\n};\n\n//修改\nexport const modifyMemberAPI = data => {\n  return request({\n    url: '/member/',\n    method: 'put',\n    data\n  });\n};\n\n//删除\nexport const removeMemberAPI = id => {\n  return request({\n    url: `/member/${id}`,\n    method: 'delete'\n  });\n};\n\n//导出Excel\nexport const exportMemberAPI = params => {\n  return request({\n    url: '/member/export',\n    method: 'get',\n    params,\n    responseType: 'blob'\n  });\n};\n\n//导入Excel\nexport const importMemberAPI = formData => {\n  return request({\n    url: '/member/import',\n    method: 'post',\n    data: formData,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\n\n//下载导入模板\nexport const downloadMemberTemplateAPI = () => {\n  return request({\n    url: '/member/template',\n    method: 'get',\n    responseType: 'blob'\n  });\n};", "map": {"version": 3, "names": ["request", "findMemberPageAPI", "pageNum", "pageSize", "params", "url", "method", "findMemberAPI", "findAllMemberAPI", "addMemberAPI", "data", "modifyMemberAPI", "removeMemberAPI", "id", "exportMemberAPI", "responseType", "importMemberAPI", "formData", "headers", "downloadMemberTemplateAPI"], "sources": ["D:/project/hotel/hotely5d-web/src/api/member.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n//根据条件分页查询\r\nexport const findMemberPageAPI = (pageNum,pageSize,params) => {\r\n    return request({\r\n        url: `/member/search/${pageNum}/${pageSize}`,\r\n        method: 'get',\r\n        params\r\n    })\r\n}\r\n\r\n//根据条件查询\r\nexport const findMemberAPI = (params) => {\r\n    return request({\r\n        url: '/member/search',\r\n        method: 'get',\r\n        params\r\n    })\r\n}\r\n\r\n//查询全部\r\nexport const findAllMemberAPI = () => {\r\n    return request({\r\n        url: '/member/',\r\n        method: 'get'\r\n    })\r\n}\r\n\r\n//新增\r\nexport const addMemberAPI = data => {\r\n    return request({\r\n        url: '/member/',\r\n        method: 'post',\r\n        data\r\n    })\r\n}\r\n\r\n//修改\r\nexport const modifyMemberAPI = data => {\r\n    return request({\r\n        url: '/member/',\r\n        method: 'put',\r\n        data\r\n    })\r\n}\r\n\r\n//删除\r\nexport const removeMemberAPI = id => {\r\n    return request({\r\n        url: `/member/${id}`,\r\n        method: 'delete'\r\n    })\r\n}\r\n\r\n//导出Excel\r\nexport const exportMemberAPI = (params) => {\r\n    return request({\r\n        url: '/member/export',\r\n        method: 'get',\r\n        params,\r\n        responseType: 'blob'\r\n    })\r\n}\r\n\r\n//导入Excel\r\nexport const importMemberAPI = (formData) => {\r\n    return request({\r\n        url: '/member/import',\r\n        method: 'post',\r\n        data: formData,\r\n        headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n        }\r\n    })\r\n}\r\n\r\n//下载导入模板\r\nexport const downloadMemberTemplateAPI = () => {\r\n    return request({\r\n        url: '/member/template',\r\n        method: 'get',\r\n        responseType: 'blob'\r\n    })\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,OAAO,EAACC,QAAQ,EAACC,MAAM,KAAK;EAC1D,OAAOJ,OAAO,CAAC;IACXK,GAAG,EAAG,kBAAiBH,OAAQ,IAAGC,QAAS,EAAC;IAC5CG,MAAM,EAAE,KAAK;IACbF;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMG,aAAa,GAAIH,MAAM,IAAK;EACrC,OAAOJ,OAAO,CAAC;IACXK,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbF;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;EAClC,OAAOR,OAAO,CAAC;IACXK,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMG,YAAY,GAAGC,IAAI,IAAI;EAChC,OAAOV,OAAO,CAAC;IACXK,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE,MAAM;IACdI;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAGD,IAAI,IAAI;EACnC,OAAOV,OAAO,CAAC;IACXK,GAAG,EAAE,UAAU;IACfC,MAAM,EAAE,KAAK;IACbI;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAGC,EAAE,IAAI;EACjC,OAAOb,OAAO,CAAC;IACXK,GAAG,EAAG,WAAUQ,EAAG,EAAC;IACpBP,MAAM,EAAE;EACZ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMQ,eAAe,GAAIV,MAAM,IAAK;EACvC,OAAOJ,OAAO,CAAC;IACXK,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbF,MAAM;IACNW,YAAY,EAAE;EAClB,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EACzC,OAAOjB,OAAO,CAAC;IACXK,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdI,IAAI,EAAEO,QAAQ;IACdC,OAAO,EAAE;MACL,cAAc,EAAE;IACpB;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAC3C,OAAOnB,OAAO,CAAC;IACXK,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbS,YAAY,EAAE;EAClB,CAAC,CAAC;AACN,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}