{"name": "vue-template", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.3.5", "core-js": "^3.8.3", "dayjs": "^1.11.10", "element-ui": "^2.15.13", "normalize.css": "^8.0.1", "vue": "^2.6.14", "vuex": "^3.6.2", "vue-router": "^3.0.7", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "vue-template-compiler": "^2.6.14"}}