.p-10 {
  padding: 10px;
}
.pr-10 {
  padding-right: 10px;
}
.pl-10 {
  padding-left: 10px;
}
.pt-10 {
  padding-top: 10px;
}
.pb-10 {
  padding-bottom: 10px;
}
.px-10 {
  padding-right: 10px;
  padding-left: 10px;
}
.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}
.p-20 {
  padding: 20px;
}
.pr-20 {
  padding-right: 20px;
}
.pl-20 {
  padding-left: 20px;
}
.pt-20 {
  padding-top: 20px;
}
.pb-20 {
  padding-bottom: 20px;
}
.px-20 {
  padding-right: 20px;
  padding-left: 20px;
}
.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.p-30 {
  padding: 30px;
}
.pr-30 {
  padding-right: 30px;
}
.pl-30 {
  padding-left: 30px;
}
.pt-30 {
  padding-top: 30px;
}
.pb-30 {
  padding-bottom: 30px;
}
.px-30 {
  padding-right: 30px;
  padding-left: 30px;
}
.py-30 {
  padding-top: 30px;
  padding-bottom: 30px;
}
.p-40 {
  padding: 40px;
}
.pr-40 {
  padding-right: 40px;
}
.pl-40 {
  padding-left: 40px;
}
.pt-40 {
  padding-top: 40px;
}
.pb-40 {
  padding-bottom: 40px;
}
.px-40 {
  padding-right: 40px;
  padding-left: 40px;
}
.py-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.m-10 {
  margin: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mt-10 {
  margin-top: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mx-10 {
  margin-right: 10px;
  margin-left: 10px;
}
.my-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.m-20 {
  margin: 20px;
}
.mr-20 {
  margin-right: 20px;
}
.ml-20 {
  margin-left: 20px;
}
.mt-20 {
  margin-top: 20px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mx-20 {
  margin-right: 20px;
  margin-left: 20px;
}
.my-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.m-30 {
  margin: 30px;
}
.mr-30 {
  margin-right: 30px;
}
.ml-30 {
  margin-left: 30px;
}
.mt-30 {
  margin-top: 30px;
}
.mb-30 {
  margin-bottom: 30px;
}
.mx-30 {
  margin-right: 30px;
  margin-left: 30px;
}
.my-30 {
  margin-top: 30px;
  margin-bottom: 30px;
}
.m-40 {
  margin: 40px;
}
.mr-40 {
  margin-right: 40px;
}
.ml-40 {
  margin-left: 40px;
}
.mt-40 {
  margin-top: 40px;
}
.mb-40 {
  margin-bottom: 40px;
}
.mx-40 {
  margin-right: 40px;
  margin-left: 40px;
}
.my-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.text-xs {
  font-size: 12px;
  line-height: 16px;
}
.text-sm {
  font-size: 14px;
  line-height: 20px;
}
.text-base {
  font-size: 16px;
  line-height: 24px;
}
.text-lg {
  font-size: 18px;
  line-height: 28px;
}
.text-xl {
  font-size: 20px;
  line-height: 28px;
}
.font-thin {
  font-weight: 100;
}
.font-normal {
  font-weight: 400;
}
.font-bold {
  font-weight: bold;
}
/* 单行隐藏 */
.text-ellipsis{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-red {
  color: red;
}
.text-blue {
  color: blue;
}
.text-green {
  color: green;
}
.text-black {
  color: black;
}
.text-white {
  color: white;
}
.text-gray {
  color: gray;
}
.text-success {
  color: #67c23a;
}
.text-error {
  color: #f56c6c;
}
.text-warning {
  color: #e6a23c;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.underline {
  text-decoration-line: underline;
}
.no-underline {
  text-decoration-line: none;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.hidden {
  display: none;
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.w-full{
  width: 100%;
}
.pointer{
  cursor: pointer;
}
