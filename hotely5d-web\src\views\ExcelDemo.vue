<template>
  <div class="excel-demo">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>EasyPoi Excel导入导出演示</span>
      </div>
      
      <!-- 会员数据导入导出 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <h3>会员数据管理</h3>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
          <el-button type="primary" @click="exportMemberData">
            <i class="el-icon-download"></i> 导出会员数据
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="downloadMemberTemplate">
            <i class="el-icon-document"></i> 下载导入模板
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-upload
            class="upload-demo"
            action=""
            :before-upload="handleMemberImport"
            :show-file-list="false"
            accept=".xls,.xlsx">
            <el-button type="warning">
              <i class="el-icon-upload2"></i> 导入会员数据
            </el-button>
          </el-upload>
        </el-col>
      </el-row>

      <el-divider></el-divider>

      <!-- 订单数据导出 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <h3>订单数据管理</h3>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" @click="exportOrderData">
            <i class="el-icon-download"></i> 导出订单数据
          </el-button>
        </el-col>
      </el-row>

      <!-- 功能说明 -->
      <el-divider></el-divider>
      <el-row>
        <el-col :span="24">
          <h3>EasyPoi功能特点</h3>
          <ul>
            <li><strong>注解驱动：</strong>通过@Excel注解配置列映射，简单易用</li>
            <li><strong>数据转换：</strong>支持数字、日期格式化，枚举值替换</li>
            <li><strong>模板导出：</strong>可以创建标准的Excel模板供用户下载</li>
            <li><strong>批量导入：</strong>支持Excel文件批量导入数据到数据库</li>
            <li><strong>错误处理：</strong>导入时自动处理数据验证和错误提示</li>
            <li><strong>性能优化：</strong>针对大数据量进行了优化处理</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { 
  exportMemberAPI, 
  importMemberAPI, 
  downloadMemberTemplateAPI 
} from '@/api/member'

export default {
  name: 'ExcelDemo',
  data() {
    return {
      loading: false
    }
  },
  methods: {
    // 导出会员数据
    async exportMemberData() {
      try {
        this.loading = true
        const response = await exportMemberAPI({})
        
        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.ms-excel'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `会员数据_${new Date().getTime()}.xls`
        link.click()
        window.URL.revokeObjectURL(url)
        
        this.$message.success('导出成功')
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 下载会员导入模板
    async downloadMemberTemplate() {
      try {
        this.loading = true
        const response = await downloadMemberTemplateAPI()
        
        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.ms-excel'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '会员导入模板.xls'
        link.click()
        window.URL.revokeObjectURL(url)
        
        this.$message.success('模板下载成功')
      } catch (error) {
        this.$message.error('下载失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 导入会员数据
    async handleMemberImport(file) {
      try {
        this.loading = true
        const formData = new FormData()
        formData.append('file', file)
        
        const response = await importMemberAPI(formData)
        
        if (response.flag) {
          this.$message.success(response.message)
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        this.$message.error('导入失败：' + error.message)
      } finally {
        this.loading = false
      }
      
      return false // 阻止自动上传
    },

    // 导出订单数据
    async exportOrderData() {
      try {
        this.loading = true
        // 这里需要调用订单导出API
        this.$message.info('订单导出功能开发中...')
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.excel-demo {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.upload-demo {
  display: inline-block;
}

ul {
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
  line-height: 1.6;
}
</style>
