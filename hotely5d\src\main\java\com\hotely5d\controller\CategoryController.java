package com.hotely5d.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.hotely5d.entity.Category;
import com.hotely5d.entity.model.Result;
import com.hotely5d.entity.model.StatusCode;
import com.hotely5d.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    /**
    * 查询全部
    * @return
    */
    @GetMapping
    public Result findAll(){
    List<Category> categoryList = categoryService.findAll();
        return new Result(true, StatusCode.OK,"查询成功",categoryList);
    }

    /**
    * 根据条件查询
    * @param category
    * @return
    */
    @GetMapping("search")
    public Result search(Category category){
        List<Category> categoryList = categoryService.search(category);
        return new Result(true, StatusCode.OK,"查询成功",categoryList);
    }

    /**
    * 根据条件分页查询
    * @param category
    * @return
    */
    @GetMapping("search/{current}/{size}")
    public Result search(@PathVariable Integer current,@PathVariable Integer size ,Category category){
        Page<Category> page = categoryService.search(new Page<Category>(current, size), category);
        return new Result(true, StatusCode.OK,"查询成功",page);
    }

    /**
    * 新增
    * @param category
    * @return
    */
    @PostMapping
    public Result add(@RequestBody Category category){
        categoryService.add(category);
        return new Result(true, StatusCode.OK,"新增成功");
    }

    /**
    * 修改
    * @param category
    * @return
    */
    @PutMapping
    public Result modify(@RequestBody Category category){
        categoryService.modify(category);
        return new Result(true, StatusCode.OK,"修改成功");
    }

    /**
    * 根据id查询
    * @param id
    * @return
    */
    @GetMapping("{id}")
    public Result findById(@PathVariable("id") Integer id){
        Category category  = categoryService.findById(id);
        return new Result(true, StatusCode.OK,"查询成功",category);
    }

    /**
    * 根据id删除
    * @param id
    * @return
    */
    @DeleteMapping("{id}")
    public Result removeById(@PathVariable("id") Integer id){
        categoryService.removeById(id);
        return new Result(true, StatusCode.OK,"删除成功");
    }

}