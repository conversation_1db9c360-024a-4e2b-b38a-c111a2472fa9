package com.hotely5d.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.hotely5d.dto.MemberExcelDto;
import com.hotely5d.entity.Member;
import com.hotely5d.entity.model.Result;
import com.hotely5d.entity.model.StatusCode;
import com.hotely5d.entity.query.LoginQuery;
import com.hotely5d.service.MemberService;
import com.hotely5d.util.ExcelUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/member")
public class MemberController {

    @Autowired
    private MemberService memberService;

    /**
     * 登录
     * @return
     */
    @PostMapping("login")
    public Result login(@RequestBody LoginQuery query){
        return memberService.login(query);
    }

    /**
    * 查询全部
    * @return
    */
    @GetMapping
    public Result findAll(){
    List<Member> memberList = memberService.findAll();
        return new Result(true, StatusCode.OK,"查询成功",memberList);
    }

    /**
    * 根据条件查询
    * @param member
    * @return
    */
    @GetMapping("search")
    public Result search(Member member){
        List<Member> memberList = memberService.search(member);
        return new Result(true, StatusCode.OK,"查询成功",memberList);
    }

    /**
    * 根据条件分页查询
    * @param member
    * @return
    */
    @GetMapping("search/{current}/{size}")
    public Result search(@PathVariable Integer current,@PathVariable Integer size ,Member member){
        Page<Member> page = memberService.search(new Page<Member>(current, size), member);
        return new Result(true, StatusCode.OK,"查询成功",page);
    }

    /**
    * 新增
    * @param member
    * @return
    */
    @PostMapping
    public Result add(@RequestBody Member member){
        memberService.add(member);
        return new Result(true, StatusCode.OK,"新增成功");
    }

    /**
    * 修改
    * @param member
    * @return
    */
    @PutMapping
    public Result modify(@RequestBody Member member){
        memberService.modify(member);
        return new Result(true, StatusCode.OK,"修改成功");
    }

    /**
    * 根据id查询
    * @param id
    * @return
    */
    @GetMapping("{id}")
    public Result findById(@PathVariable("id") Integer id){
        Member member  = memberService.findById(id);
        return new Result(true, StatusCode.OK,"查询成功",member);
    }

    /**
    * 根据id删除
    * @param id
    * @return
    */
    @DeleteMapping("{id}")
    public Result removeById(@PathVariable("id") Integer id){
        memberService.removeById(id);
        return new Result(true, StatusCode.OK,"删除成功");
    }

    /**
     * 导出会员数据到Excel
     * @param member 查询条件
     * @param response 响应对象
     */
    @GetMapping("/export")
    public void exportExcel(Member member, HttpServletResponse response) {
        try {
            // 查询数据
            List<Member> memberList = memberService.search(member);

            // 转换为Excel DTO
            List<MemberExcelDto> excelList = new ArrayList<>();
            for (Member m : memberList) {
                MemberExcelDto dto = new MemberExcelDto();
                BeanUtils.copyProperties(m, dto);
                excelList.add(dto);
            }

            // 导出Excel
            String fileName = "会员数据_" + System.currentTimeMillis() + ".xls";
            ExcelUtil.exportExcel(excelList, "会员数据", "会员信息",
                                MemberExcelDto.class, fileName, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 从Excel导入会员数据
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    public Result importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 解析Excel文件
            List<MemberExcelDto> excelList = ExcelUtil.importExcel(file, 1, 1, MemberExcelDto.class);

            if (excelList == null || excelList.isEmpty()) {
                return new Result(false, StatusCode.ERROR, "导入数据为空");
            }

            // 转换为实体对象并保存
            List<Member> memberList = new ArrayList<>();
            for (MemberExcelDto dto : excelList) {
                Member member = new Member();
                BeanUtils.copyProperties(dto, member);

                // 设置默认值
                if (member.getCreateTime() == null) {
                    member.setCreateTime(new Date());
                }
                if (member.getPassword() == null || member.getPassword().trim().isEmpty()) {
                    member.setPassword("123456"); // 默认密码
                }

                memberList.add(member);
            }

            // 批量保存
            int successCount = 0;
            for (Member member : memberList) {
                try {
                    memberService.add(member);
                    successCount++;
                } catch (Exception e) {
                    // 记录失败的数据，继续处理其他数据
                    System.err.println("导入会员失败：" + member.getUsername() + "，原因：" + e.getMessage());
                }
            }

            return new Result(true, StatusCode.OK,
                            String.format("导入完成，成功导入%d条数据，共%d条数据", successCount, memberList.size()));

        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "导入失败：" + e.getMessage());
        }
    }

    /**
     * 下载会员导入模板
     * @param response 响应对象
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 创建模板数据
            List<MemberExcelDto> templateList = new ArrayList<>();
            MemberExcelDto template = new MemberExcelDto();
            template.setUsername("示例账号");
            template.setName("示例姓名");
            template.setGender(1);
            template.setPhone("13800138000");
            template.setIdcard("110101199001011234");
            template.setCreateTime(new Date());
            templateList.add(template);

            // 导出模板
            String fileName = "会员导入模板.xls";
            ExcelUtil.exportExcel(templateList, "会员导入模板", "会员信息",
                                MemberExcelDto.class, fileName, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}