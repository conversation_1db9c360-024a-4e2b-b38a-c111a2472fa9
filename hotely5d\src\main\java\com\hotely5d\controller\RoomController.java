package com.hotely5d.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.hotely5d.entity.Room;
import com.hotely5d.entity.model.Result;
import com.hotely5d.entity.model.StatusCode;
import com.hotely5d.entity.query.RoomQuery;
import com.hotely5d.service.AppointmentService;
import com.hotely5d.service.RoomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/room")
public class RoomController {

    @Autowired
    private RoomService roomService;

    @Autowired
    private AppointmentService appointmentService;

    /**
    * 查询全部
    * @return
    */
    @GetMapping
    public Result findAll(){
    List<Room> roomList = roomService.findAll();
        return new Result(true, StatusCode.OK,"查询成功",roomList);
    }

    /**
    * 根据条件查询
    * @param room
    * @return
    */
    @GetMapping("search")
    public Result search(Room room){
        List<Room> roomList = roomService.search(room);
        return new Result(true, StatusCode.OK,"查询成功",roomList);
    }

    /**
    * 根据条件分页查询
    * @param room
    * @return
    */
    @GetMapping("search/{current}/{size}")
    public Result search(@PathVariable Integer current,@PathVariable Integer size ,Room room){
        Page<Room> page = roomService.search(new Page<Room>(current, size), room);
        return new Result(true, StatusCode.OK,"查询成功",page);
    }

    /**
    * 新增
    * @param room
    * @return
    */
    @PostMapping
    public Result add(@RequestBody Room room){
        return roomService.add(room);
    }

    /**
    * 修改
    * @param room
    * @return
    */
    @PutMapping
    public Result modify(@RequestBody Room room){
        return roomService.modify(room);
    }

    /**
    * 根据id查询
    * @param id
    * @return
    */
    @GetMapping("{id}")
    public Result findById(@PathVariable("id") Integer id){
        Room room  = roomService.findById(id);
        return new Result(true, StatusCode.OK,"查询成功",room);
    }

    /**
    * 根据id删除
    * @param id
    * @return
    */
    @DeleteMapping("{id}")
    public Result removeById(@PathVariable("id") Integer id){
        roomService.removeById(id);
        return new Result(true, StatusCode.OK,"删除成功");
    }

    /**
     * 查询房间列表、关联查询房型、房间是否可预约
     * @return
     */
    @GetMapping("findRoomList")
    public Result findRoomList(RoomQuery roomQuery){
        List<Room> list = appointmentService.findRoomList(roomQuery);
        return new Result(true, StatusCode.OK,"查询成功",list);
    }

}