package com.hotely5d.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 会员Excel导入导出DTO
 */
@ExcelTarget("memberExcel")
public class MemberExcelDto {

    @Excel(name = "编号", orderNum = "0", width = 10)
    private Integer id;

    @Excel(name = "账号", orderNum = "1", width = 20, needMerge = true)
    private String username;

    @Excel(name = "姓名", orderNum = "2", width = 15, needMerge = true)
    private String name;

    @Excel(name = "性别", orderNum = "3", width = 10, replace = {"男_1", "女_2"})
    private Integer gender;

    @Excel(name = "手机号", orderNum = "4", width = 20)
    private String phone;

    @Excel(name = "身份证号", orderNum = "5", width = 25)
    private String idcard;

    @Excel(name = "注册时间", orderNum = "6", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public MemberExcelDto() {
    }

    public MemberExcelDto(Integer id, String username, String name, Integer gender, 
                         String phone, String idcard, Date createTime) {
        this.id = id;
        this.username = username;
        this.name = name;
        this.gender = gender;
        this.phone = phone;
        this.idcard = idcard;
        this.createTime = createTime;
    }

    // Getter and Setter methods
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
