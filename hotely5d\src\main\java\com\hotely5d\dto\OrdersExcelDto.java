package com.hotely5d.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单Excel导入导出DTO
 */
@ExcelTarget("ordersExcel")
public class OrdersExcelDto {

    @Excel(name = "订单编号", orderNum = "0", width = 15)
    private Integer id;

    @Excel(name = "会员账号", orderNum = "1", width = 20)
    private String memberUsername;

    @Excel(name = "会员姓名", orderNum = "2", width = 15)
    private String memberName;

    @Excel(name = "房间号", orderNum = "3", width = 15)
    private Integer roomNum;

    @Excel(name = "房型名称", orderNum = "4", width = 20)
    private String categoryName;

    @Excel(name = "入住时间", orderNum = "5", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Excel(name = "居住天数", orderNum = "6", width = 15)
    private Integer days;

    @Excel(name = "订单状态", orderNum = "7", width = 15, replace = {"已预订_1", "已入住_2", "已退房_3"})
    private Integer status;

    @Excel(name = "已付金额", orderNum = "8", width = 15, numFormat = "0.00")
    private BigDecimal money;

    @Excel(name = "退房时间", orderNum = "9", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkOutTime;

    @Excel(name = "备注", orderNum = "10", width = 30)
    private String remark;

    public OrdersExcelDto() {
    }

    public OrdersExcelDto(Integer id, String memberUsername, String memberName, Integer roomNum, 
                         String categoryName, Date startTime, Integer days, Integer status, 
                         BigDecimal money, Date checkOutTime, String remark) {
        this.id = id;
        this.memberUsername = memberUsername;
        this.memberName = memberName;
        this.roomNum = roomNum;
        this.categoryName = categoryName;
        this.startTime = startTime;
        this.days = days;
        this.status = status;
        this.money = money;
        this.checkOutTime = checkOutTime;
        this.remark = remark;
    }

    // Getter and Setter methods
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMemberUsername() {
        return memberUsername;
    }

    public void setMemberUsername(String memberUsername) {
        this.memberUsername = memberUsername;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Integer getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(Integer roomNum) {
        this.roomNum = roomNum;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public Date getCheckOutTime() {
        return checkOutTime;
    }

    public void setCheckOutTime(Date checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
