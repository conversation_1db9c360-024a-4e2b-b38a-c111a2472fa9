package com.hotely5d.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

/**
 * Excel工具类
 */
public class ExcelUtil {

    /**
     * 导出Excel
     *
     * @param list      导出数据集合
     * @param title     标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName  文件名
     * @param response  响应对象
     */
    public static void exportExcel(List<?> list, String title, String sheetName, 
                                 Class<?> pojoClass, String fileName, HttpServletResponse response) throws IOException {
        defaultExport(list, pojoClass, fileName, response, new ExportParams(title, sheetName));
    }

    /**
     * 导出Excel
     *
     * @param list      导出数据集合
     * @param title     标题
     * @param sheetName sheet名称
     * @param pojoClass pojo类型
     * @param fileName  文件名
     * @param isCreateHeader 是否创建表头
     * @param response  响应对象
     */
    public static void exportExcel(List<?> list, String title, String sheetName, 
                                 Class<?> pojoClass, String fileName, boolean isCreateHeader, 
                                 HttpServletResponse response) throws IOException {
        ExportParams exportParams = new ExportParams(title, sheetName);
        exportParams.setCreateHeadRows(isCreateHeader);
        defaultExport(list, pojoClass, fileName, response, exportParams);
    }

    /**
     * 导出Excel
     *
     * @param list     导出数据集合
     * @param fileName 文件名
     * @param response 响应对象
     */
    public static void exportExcel(List<Map<String, Object>> list, String fileName, 
                                 HttpServletResponse response) throws IOException {
        defaultExport(list, fileName, response);
    }

    /**
     * 默认的导出Excel
     *
     * @param list         导出数据集合
     * @param pojoClass    pojo类型
     * @param fileName     文件名
     * @param response     响应对象
     * @param exportParams 导出参数
     */
    private static void defaultExport(List<?> list, Class<?> pojoClass, String fileName, 
                                    HttpServletResponse response, ExportParams exportParams) throws IOException {
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, pojoClass, list);
        if (workbook != null) {
            downLoadExcel(fileName, response, workbook);
        }
    }

    /**
     * 默认的导出Excel
     *
     * @param list     导出数据集合
     * @param fileName 文件名
     * @param response 响应对象
     */
    private static void defaultExport(List<Map<String, Object>> list, String fileName, 
                                    HttpServletResponse response) throws IOException {
        Workbook workbook = ExcelExportUtil.exportExcel(list, ExcelType.HSSF);
        if (workbook != null) {
            downLoadExcel(fileName, response, workbook);
        }
    }

    /**
     * 下载Excel
     *
     * @param fileName 文件名
     * @param response 响应对象
     * @param workbook 工作簿
     */
    private static void downLoadExcel(String fileName, HttpServletResponse response, 
                                    Workbook workbook) throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new IOException(e.getMessage());
        }
    }

    /**
     * 导入Excel
     *
     * @param filePath   文件路径
     * @param titleRows  标题行数
     * @param headerRows 表头行数
     * @param pojoClass  pojo类型
     * @return 导入数据列表
     */
    public static <T> List<T> importExcel(String filePath, Integer titleRows, Integer headerRows, 
                                        Class<T> pojoClass) {
        if (filePath == null || "".equals(filePath)) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        List<T> list = null;
        try {
            list = ExcelImportUtil.importExcel(filePath, pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new RuntimeException("模板不能为空");
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        return list;
    }

    /**
     * 导入Excel
     *
     * @param file      上传的文件
     * @param titleRows 标题行数
     * @param headerRows 表头行数
     * @param pojoClass pojo类型
     * @return 导入数据列表
     */
    public static <T> List<T> importExcel(MultipartFile file, Integer titleRows, Integer headerRows, 
                                        Class<T> pojoClass) {
        if (file == null) {
            return null;
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        List<T> list = null;
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), pojoClass, params);
        } catch (NoSuchElementException e) {
            throw new RuntimeException("Excel文件不能为空");
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        return list;
    }
}
