<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hotely5d.dao.AdminMapper" >

    <!--基础resultMap -->
    <resultMap id="baseResultMap" type="com.hotely5d.entity.Admin">
	        <id column="id" property="id" jdbcType="INTEGER" />
	        <result column="username" property="username" jdbcType="VARCHAR" />
	        <result column="password" property="password" jdbcType="VARCHAR" />
	        <result column="name" property="name" jdbcType="VARCHAR" />
	        <result column="gender" property="gender" jdbcType="INTEGER" />
    </resultMap>

</mapper>