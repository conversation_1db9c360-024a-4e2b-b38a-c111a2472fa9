<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hotely5d.dao.CategoryMapper" >

    <!--基础resultMap -->
    <resultMap id="baseResultMap" type="com.hotely5d.entity.Category">
	        <id column="id" property="id" jdbcType="INTEGER" />
	        <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
	        <result column="photo" property="photo" jdbcType="VARCHAR" />
	        <result column="price" property="price" jdbcType="DECIMAL" />
	        <result column="live_num" property="liveNum" jdbcType="INTEGER" />
	        <result column="bed_num" property="bedNum" jdbcType="INTEGER" />
	        <result column="area" property="area" jdbcType="VARCHAR" />
	        <result column="introduce" property="introduce" jdbcType="VARCHAR" />
    </resultMap>

</mapper>