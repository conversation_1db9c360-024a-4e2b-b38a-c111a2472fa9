<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hotely5d.dao.NoticeMapper" >

    <!--基础resultMap -->
    <resultMap id="baseResultMap" type="com.hotely5d.entity.Notice">
	        <id column="id" property="id" jdbcType="INTEGER" />
	        <result column="content" property="content" jdbcType="VARCHAR" />
	        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
            <result column="title" property="title" jdbcType="VARCHAR" />
    </resultMap>

</mapper>