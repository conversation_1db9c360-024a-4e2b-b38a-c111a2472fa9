<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hotely5d.dao.AppointmentMapper" >

    <!--基础resultMap -->
    <resultMap id="baseResultMap" type="com.hotely5d.entity.Appointment">
	        <id column="id" property="id" jdbcType="INTEGER" />
	        <result column="member_id" property="memberId" jdbcType="INTEGER" />
	        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
	        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
	        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
	        <result column="status" property="status" jdbcType="INTEGER" />
	        <result column="remark" property="remark" jdbcType="VARCHAR" />
	        <result column="money" property="money" jdbcType="DECIMAL" />
    </resultMap>

</mapper>