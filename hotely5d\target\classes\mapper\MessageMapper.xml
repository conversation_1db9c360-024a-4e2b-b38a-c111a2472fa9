<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hotely5d.dao.MessageMapper" >

    <!--基础resultMap -->
    <resultMap id="baseResultMap" type="com.hotely5d.entity.Message">
	        <id column="id" property="id" jdbcType="INTEGER" />
	        <result column="member_id" property="memberId" jdbcType="INTEGER" />
	        <result column="comment" property="comment" jdbcType="VARCHAR" />
	        <result column="reply" property="reply" jdbcType="VARCHAR" />
	        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
	        <result column="reply_time" property="replyTime" jdbcType="TIMESTAMP" />
	        <result column="status" property="status" jdbcType="INTEGER" />
    </resultMap>

</mapper>